#!/bin/bash

# 交互式运行脚本 - 分步执行分析任务
# 用户可以选择要执行的步骤

echo "=========================================="
echo "大数据实时处理技术实训 - 交互式运行"
echo "=========================================="

# 设置环境变量
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.412.b08-1.el7_9.x86_64
export SPARK_HOME=/opt/spark
export PATH=$SPARK_HOME/bin:$PATH

# 显示菜单
show_menu() {
    echo ""
    echo "请选择要执行的任务："
    echo "1. 生成人口年龄数据"
    echo "2. 运行人口年龄分析（spark-shell）"
    echo "3. 运行咖啡数据分析（spark-shell）"
    echo "4. 查看分析结果"
    echo "5. 运行全部任务"
    echo "6. 退出"
    echo ""
}

# 生成人口年龄数据
generate_people_data() {
    echo "生成人口年龄数据..."
    if [ -f "GeneratePeopleAge.scala" ]; then
        scalac GeneratePeopleAge.scala
        if [ $? -eq 0 ]; then
            scala GeneratePeopleAge 1000 peopleage.txt
            echo "✓ 人口年龄数据生成完成！"
        else
            echo "✗ 编译失败！"
        fi
    else
        echo "✗ 找不到 GeneratePeopleAge.scala 文件！"
    fi
}

# 运行人口年龄分析
run_age_analysis() {
    echo "运行人口年龄分析..."
    if [ -f "CalculateAverageAge.scala" ] && [ -f "peopleage.txt" ]; then
        echo "启动spark-shell进行人口年龄分析..."
        echo "提示：分析完成后请输入 :quit 退出spark-shell"
        echo ""
        
        spark-shell --master local[*] \
                    --conf spark.ui.enabled=false \
                    --conf spark.sql.warehouse.dir=/tmp/spark-warehouse \
                    -i CalculateAverageAge.scala
        
        echo "✓ 人口年龄分析完成！"
    else
        echo "✗ 缺少必要文件！请先生成人口年龄数据。"
    fi
}

# 运行咖啡数据分析
run_coffee_analysis() {
    echo "运行咖啡数据分析..."
    if [ -f "CoffeeChainAnalysis.scala" ] && [ -f "CoffeeChain.csv" ]; then
        echo "启动spark-shell进行咖啡数据分析..."
        echo "提示：分析完成后请输入 :quit 退出spark-shell"
        echo ""
        
        spark-shell --master local[*] \
                    --conf spark.ui.enabled=false \
                    --conf spark.sql.warehouse.dir=/tmp/spark-warehouse \
                    -i CoffeeChainAnalysis.scala \
                    --conf spark.driver.memory=2g
        
        echo "✓ 咖啡数据分析完成！"
    else
        echo "✗ 缺少必要文件（CoffeeChainAnalysis.scala 或 CoffeeChain.csv）！"
    fi
}

# 查看分析结果
view_results() {
    echo "查看分析结果..."
    echo ""
    
    if [ -f "peopleage.txt" ]; then
        echo "=== 人口年龄数据（前10行）==="
        head -10 peopleage.txt
        echo ""
    fi
    
    if [ -f "age_analysis_results.txt" ]; then
        echo "=== 人口年龄分析结果 ==="
        cat age_analysis_results.txt
        echo ""
    fi
    
    if [ -f "coffee_analysis_results.txt" ]; then
        echo "=== 咖啡分析结果（前50行）==="
        head -50 coffee_analysis_results.txt
        echo ""
        echo "完整结果请查看 coffee_analysis_results.txt 文件"
    fi
}

# 运行全部任务
run_all() {
    echo "运行全部任务..."
    echo ""
    
    echo "步骤1: 生成人口年龄数据"
    generate_people_data
    echo ""
    
    echo "步骤2: 运行人口年龄分析"
    echo "注意：这将启动spark-shell，分析完成后会自动退出"
    read -p "按回车键继续..."
    run_age_analysis
    echo ""
    
    echo "步骤3: 运行咖啡数据分析"
    echo "注意：这将启动spark-shell，分析完成后会自动退出"
    read -p "按回车键继续..."
    run_coffee_analysis
    echo ""
    
    echo "步骤4: 查看结果"
    view_results
    
    echo "✓ 所有任务完成！"
}

# 主循环
while true; do
    show_menu
    read -p "请输入选项 (1-6): " choice
    
    case $choice in
        1)
            generate_people_data
            ;;
        2)
            run_age_analysis
            ;;
        3)
            run_coffee_analysis
            ;;
        4)
            view_results
            ;;
        5)
            run_all
            ;;
        6)
            echo "退出程序。"
            exit 0
            ;;
        *)
            echo "无效选项，请重新选择。"
            ;;
    esac
    
    echo ""
    read -p "按回车键继续..."
done
