#!/bin/bash

# 大数据实时处理技术实训 - 运行脚本
# 作者：lbxx
# 日期：2024年

echo "=========================================="
echo "大数据实时处理技术实训 - 自动化运行脚本"
echo "=========================================="

# 设置环境变量
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.412.b08-1.el7_9.x86_64
export SPARK_HOME=/opt/spark
export PATH=$SPARK_HOME/bin:$PATH

# 检查环境
echo "检查环境配置..."
echo "JAVA_HOME: $JAVA_HOME"
echo "SPARK_HOME: $SPARK_HOME"

if [ ! -d "$JAVA_HOME" ]; then
    echo "错误：Java路径不存在！"
    exit 1
fi

if [ ! -d "$SPARK_HOME" ]; then
    echo "错误：Spark路径不存在！"
    exit 1
fi

echo "环境检查通过！"
echo ""

# 第一部分：RDD编程统计人口平均年龄
echo "=========================================="
echo "第一部分：RDD编程统计人口平均年龄"
echo "=========================================="

echo "1. 生成人口年龄数据..."
if [ -f "GeneratePeopleAge.scala" ]; then
    echo "编译 GeneratePeopleAge.scala..."
    scalac GeneratePeopleAge.scala
    if [ $? -eq 0 ]; then
        echo "运行数据生成程序..."
        scala GeneratePeopleAge 1000 peopleage.txt
        echo "数据生成完成！"
    else
        echo "编译失败！"
        exit 1
    fi
else
    echo "错误：找不到 GeneratePeopleAge.scala 文件！"
    exit 1
fi

echo ""
echo "2. 计算平均年龄..."
if [ -f "CalculateAverageAge.scala" ]; then
    echo "使用 spark-submit 运行年龄计算程序..."
    
    # 编译Scala文件
    echo "编译 CalculateAverageAge.scala..."
    scalac -cp "$SPARK_HOME/jars/*" CalculateAverageAge.scala
    
    if [ $? -eq 0 ]; then
        # 创建JAR文件
        echo "创建JAR文件..."
        jar cf CalculateAverageAge.jar CalculateAverageAge*.class
        
        # 运行Spark应用
        echo "运行Spark应用..."
        spark-submit --class CalculateAverageAge --master local[*] CalculateAverageAge.jar peopleage.txt
        
        echo "平均年龄计算完成！"
    else
        echo "编译失败！"
        exit 1
    fi
else
    echo "错误：找不到 CalculateAverageAge.scala 文件！"
    exit 1
fi

echo ""

# 第二部分：咖啡连锁店数据分析
echo "=========================================="
echo "第二部分：咖啡连锁店数据分析"
echo "=========================================="

echo "检查数据文件..."
if [ ! -f "CoffeeChain.csv" ]; then
    echo "错误：找不到 CoffeeChain.csv 数据文件！"
    exit 1
fi

echo "数据文件检查通过！"
echo ""

if [ -f "CoffeeChainAnalysis.scala" ]; then
    echo "编译 CoffeeChainAnalysis.scala..."
    scalac -cp "$SPARK_HOME/jars/*" CoffeeChainAnalysis.scala
    
    if [ $? -eq 0 ]; then
        # 创建JAR文件
        echo "创建JAR文件..."
        jar cf CoffeeChainAnalysis.jar CoffeeChainAnalysis*.class
        
        # 运行Spark应用
        echo "运行咖啡连锁店数据分析..."
        spark-submit --class CoffeeChainAnalysis --master local[*] CoffeeChainAnalysis.jar CoffeeChain.csv
        
        echo "咖啡连锁店数据分析完成！"
    else
        echo "编译失败！"
        exit 1
    fi
else
    echo "错误：找不到 CoffeeChainAnalysis.scala 文件！"
    exit 1
fi

echo ""
echo "=========================================="
echo "所有分析任务完成！"
echo "=========================================="

echo "生成的文件："
echo "- peopleage.txt: 人口年龄模拟数据"
echo "- coffee_analysis_results.txt: 咖啡连锁店分析结果"

echo ""
echo "查看结果文件："
if [ -f "coffee_analysis_results.txt" ]; then
    echo "咖啡分析结果文件大小：$(wc -l < coffee_analysis_results.txt) 行"
    echo "前20行预览："
    head -20 coffee_analysis_results.txt
fi

if [ -f "peopleage.txt" ]; then
    echo ""
    echo "人口年龄数据文件大小：$(wc -l < peopleage.txt) 行"
    echo "前10行预览："
    head -10 peopleage.txt
fi

echo ""
echo "实训任务执行完成！请查看生成的结果文件。"
