#!/bin/bash

# 快速测试脚本 - 验证所有程序能否正常运行
# 用于在提交前进行最终验证

echo "=========================================="
echo "大数据实时处理技术实训 - 快速测试"
echo "=========================================="

# 设置环境变量
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.412.b08-1.el7_9.x86_64
export SPARK_HOME=/opt/spark
export PATH=$SPARK_HOME/bin:$PATH

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试结果统计
TESTS_PASSED=0
TESTS_FAILED=0

# 测试函数
test_step() {
    local step_name="$1"
    local command="$2"
    
    echo -n "测试 $step_name... "
    
    if eval "$command" >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 通过${NC}"
        ((TESTS_PASSED++))
        return 0
    else
        echo -e "${RED}✗ 失败${NC}"
        ((TESTS_FAILED++))
        return 1
    fi
}

# 文件存在性检查
check_file() {
    local file="$1"
    local description="$2"
    
    echo -n "检查 $description... "
    if [ -f "$file" ]; then
        echo -e "${GREEN}✓ 存在${NC}"
        ((TESTS_PASSED++))
        return 0
    else
        echo -e "${RED}✗ 缺失${NC}"
        ((TESTS_FAILED++))
        return 1
    fi
}

echo "1. 文件完整性检查"
echo "----------------------------------------"

# 检查必需文件
check_file "GeneratePeopleAge.scala" "数据生成程序"
check_file "CalculateAverageAge.scala" "人口年龄分析程序"
check_file "CoffeeChainAnalysis.scala" "咖啡数据分析程序"
check_file "CoffeeChain.csv" "咖啡数据文件"
check_file "run_interactive.sh" "交互式运行脚本"
check_file "Spark-Shell运行说明.md" "运行说明文档"

echo ""
echo "2. 环境检查"
echo "----------------------------------------"

test_step "Java环境" "java -version"
test_step "Scala环境" "scala -version"
test_step "Spark环境" "spark-shell --version"

echo ""
echo "3. 编译测试"
echo "----------------------------------------"

# 测试数据生成程序编译
test_step "数据生成程序编译" "scalac GeneratePeopleAge.scala"

# 清理编译文件
rm -f GeneratePeopleAge*.class 2>/dev/null

echo ""
echo "4. 数据生成测试"
echo "----------------------------------------"

# 生成少量测试数据
echo -n "生成测试数据... "
if scalac GeneratePeopleAge.scala && scala GeneratePeopleAge 10 test_people.txt >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 成功${NC}"
    ((TESTS_PASSED++))
    
    # 验证数据格式
    echo -n "验证数据格式... "
    if [ -f "test_people.txt" ] && [ $(wc -l < test_people.txt) -eq 10 ]; then
        echo -e "${GREEN}✓ 正确${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}✗ 错误${NC}"
        ((TESTS_FAILED++))
    fi
else
    echo -e "${RED}✗ 失败${NC}"
    ((TESTS_FAILED++))
fi

# 清理测试文件
rm -f GeneratePeopleAge*.class test_people.txt 2>/dev/null

echo ""
echo "5. Spark连接测试"
echo "----------------------------------------"

echo -n "测试Spark本地模式连接... "
if timeout 30 spark-shell --master local[1] --conf spark.ui.enabled=false <<< "println(\"Spark连接成功\"); System.exit(0)" >/dev/null 2>&1; then
    echo -e "${GREEN}✓ 成功${NC}"
    ((TESTS_PASSED++))
else
    echo -e "${RED}✗ 失败${NC}"
    ((TESTS_FAILED++))
fi

echo ""
echo "6. 脚本权限检查"
echo "----------------------------------------"

scripts=("run_interactive.sh" "run_spark_shell.sh" "test_environment.sh")
for script in "${scripts[@]}"; do
    if [ -f "$script" ]; then
        echo -n "检查 $script 权限... "
        if [ -x "$script" ]; then
            echo -e "${GREEN}✓ 可执行${NC}"
            ((TESTS_PASSED++))
        else
            echo -e "${YELLOW}! 需要权限${NC}"
            chmod +x "$script"
            echo "  已自动添加执行权限"
            ((TESTS_PASSED++))
        fi
    fi
done

echo ""
echo "7. 数据文件检查"
echo "----------------------------------------"

if [ -f "CoffeeChain.csv" ]; then
    echo -n "检查CSV文件格式... "
    # 检查文件是否有内容且格式正确
    if [ $(wc -l < CoffeeChain.csv) -gt 1 ] && head -1 CoffeeChain.csv | grep -q ","; then
        echo -e "${GREEN}✓ 格式正确${NC}"
        ((TESTS_PASSED++))
        
        # 显示文件信息
        lines=$(wc -l < CoffeeChain.csv)
        size=$(du -h CoffeeChain.csv | cut -f1)
        echo "  文件行数: $lines"
        echo "  文件大小: $size"
    else
        echo -e "${RED}✗ 格式错误${NC}"
        ((TESTS_FAILED++))
    fi
fi

echo ""
echo "=========================================="
echo "测试结果汇总"
echo "=========================================="

total_tests=$((TESTS_PASSED + TESTS_FAILED))
success_rate=$((TESTS_PASSED * 100 / total_tests))

echo "总测试数: $total_tests"
echo -e "通过测试: ${GREEN}$TESTS_PASSED${NC}"
echo -e "失败测试: ${RED}$TESTS_FAILED${NC}"
echo "成功率: $success_rate%"

echo ""
if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "${GREEN}✓ 所有测试通过！环境配置正确，可以开始实训任务。${NC}"
    echo ""
    echo "建议的运行步骤："
    echo "1. 运行交互式脚本: ./run_interactive.sh"
    echo "2. 或查看详细说明: cat Spark-Shell运行说明.md"
    exit 0
else
    echo -e "${RED}✗ 有 $TESTS_FAILED 个测试失败，请检查环境配置。${NC}"
    echo ""
    echo "常见问题解决："
    echo "1. 检查Java和Spark环境变量"
    echo "2. 确保所有必需文件存在"
    echo "3. 检查文件权限"
    echo "4. 查看详细错误信息"
    exit 1
fi
