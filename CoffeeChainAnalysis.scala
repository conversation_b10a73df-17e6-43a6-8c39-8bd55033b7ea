import org.apache.spark.SparkContext
import org.apache.spark.SparkConf
import java.io.PrintWriter
import java.io.File

/**
 * 咖啡连锁店数据分析
 * 功能：对CoffeeChain.csv进行多维度数据分析
 */
object CoffeeChainAnalysis {
  
  // 定义咖啡数据的案例类
  case class CoffeeData(
    areaCode: String,
    date: String,
    market: String,
    marketSize: String,
    product: String,
    productType: String,
    state: String,
    coffeeType: String,
    budgetCogs: Double,
    budgetMargin: Double,
    budgetProfit: Double,
    budgetSales: Double,
    coffeeSales: Double,
    cogs: Double,
    inventory: Double,
    margin: Double,
    marketing: Double,
    numRecords: Int,
    profit: Double,
    totalExpenses: Double
  )
  
  def main(args: Array[String]): Unit = {
    // 设置Spark配置
    val conf = new SparkConf()
      .setAppName("CoffeeChainAnalysis")
      .setMaster("local[*]")
    
    val sc = new SparkContext(conf)
    
    try {
      val inputFile = if (args.length > 0) args(0) else "CoffeeChain.csv"
      
      println("开始分析咖啡连锁店数据...")
      println(s"输入文件：$inputFile")
      
      // 读取CSV文件
      val rawData = sc.textFile(inputFile)
      
      // 跳过标题行并解析数据
      val header = rawData.first()
      val dataRDD = rawData.filter(_ != header).map(parseCSVLine).filter(_.isDefined).map(_.get)
      
      // 缓存数据，因为会多次使用
      dataRDD.cache()
      
      val totalRecords = dataRDD.count()
      println(s"有效数据记录数：$totalRecords")
      
      // 创建输出文件
      val outputWriter = new PrintWriter(new File("coffee_analysis_results.txt"))
      
      try {
        // 1. 数据预处理 - 基本统计信息
        println("\n1. 数据预处理和基本统计...")
        dataPreprocessing(dataRDD, outputWriter)
        
        // 2. 咖啡销售量排名
        println("\n2. 咖啡销售量排名分析...")
        salesRanking(dataRDD, outputWriter)
        
        // 3. 销售量与州的关系
        println("\n3. 咖啡销售量与州的关系分析...")
        salesByState(dataRDD, outputWriter)
        
        // 4. 销售量与市场的关系
        println("\n4. 咖啡销售量与市场的关系分析...")
        salesByMarket(dataRDD, outputWriter)
        
        // 5. 平均利润和售价分析
        println("\n5. 咖啡平均利润和售价分析...")
        profitAndPriceAnalysis(dataRDD, outputWriter)
        
        // 6. 利润、售价和销售量关系
        println("\n6. 利润、售价和销售量关系分析...")
        profitSalesPriceRelation(dataRDD, outputWriter)
        
        // 7. 利润、销售量与成本关系
        println("\n7. 利润、销售量与成本关系分析...")
        profitSalesCostRelation(dataRDD, outputWriter)
        
        // 8. 产品属性与各指标关系
        println("\n8. 产品属性与各指标关系分析...")
        productAttributeAnalysis(dataRDD, outputWriter)
        
        // 9. 市场规模、地域与销售量关系
        println("\n9. 市场规模、地域与销售量关系分析...")
        marketSizeRegionAnalysis(dataRDD, outputWriter)
        
        println("\n分析完成！结果已保存到 coffee_analysis_results.txt")
        
      } finally {
        outputWriter.close()
      }
      
    } catch {
      case e: Exception =>
        println(s"分析过程中发生错误：${e.getMessage}")
        e.printStackTrace()
    } finally {
      sc.stop()
    }
  }
  
  // 解析CSV行数据
  def parseCSVLine(line: String): Option[CoffeeData] = {
    try {
      val parts = line.split(",")
      if (parts.length >= 20) {
        // 处理包含逗号的数字（如"1,007"）
        def parseNumber(str: String): Double = {
          str.replaceAll("\"", "").replaceAll(",", "").toDouble
        }
        
        Some(CoffeeData(
          areaCode = parts(0),
          date = parts(1),
          market = parts(2),
          marketSize = parts(3),
          product = parts(4),
          productType = parts(5),
          state = parts(6),
          coffeeType = parts(7),
          budgetCogs = parseNumber(parts(8)),
          budgetMargin = parseNumber(parts(9)),
          budgetProfit = parseNumber(parts(10)),
          budgetSales = parseNumber(parts(11)),
          coffeeSales = parseNumber(parts(12)),
          cogs = parseNumber(parts(13)),
          inventory = parseNumber(parts(14)),
          margin = parseNumber(parts(15)),
          marketing = parseNumber(parts(16)),
          numRecords = parts(17).toInt,
          profit = parseNumber(parts(18)),
          totalExpenses = parseNumber(parts(19))
        ))
      } else {
        None
      }
    } catch {
      case _: Exception => None
    }
  }
  
  // 1. 数据预处理
  def dataPreprocessing(dataRDD: org.apache.spark.rdd.RDD[CoffeeData], writer: PrintWriter): Unit = {
    writer.println("="*60)
    writer.println("1. 数据预处理和基本统计信息")
    writer.println("="*60)

    val totalRecords = dataRDD.count()
    val uniqueStates = dataRDD.map(_.state).distinct().count()
    val uniqueProducts = dataRDD.map(_.product).distinct().count()
    val uniqueMarkets = dataRDD.map(_.market).distinct().count()

    val totalSales = dataRDD.map(_.coffeeSales).sum()
    val totalProfit = dataRDD.map(_.profit).sum()
    val avgSales = totalSales / totalRecords
    val avgProfit = totalProfit / totalRecords

    writer.println(f"总记录数: $totalRecords%,d")
    writer.println(f"涉及州数: $uniqueStates")
    writer.println(f"产品种类数: $uniqueProducts")
    writer.println(f"市场数: $uniqueMarkets")
    writer.println(f"总销售额: $$${totalSales}%,.2f")
    writer.println(f"总利润: $$${totalProfit}%,.2f")
    writer.println(f"平均销售额: $$${avgSales}%.2f")
    writer.println(f"平均利润: $$${avgProfit}%.2f")
    writer.println()

    // 显示各州分布
    writer.println("各州数据分布:")
    val stateDistribution = dataRDD.map(_.state).countByValue()
    stateDistribution.toSeq.sortBy(-_._2).foreach { case (state, count) =>
      writer.println(f"  $state%-15s: $count%,6d 条记录")
    }
    writer.println()
  }

  // 2. 咖啡销售量排名
  def salesRanking(dataRDD: org.apache.spark.rdd.RDD[CoffeeData], writer: PrintWriter): Unit = {
    writer.println("="*60)
    writer.println("2. 咖啡销售量排名分析")
    writer.println("="*60)

    // 按产品排名
    writer.println("按产品销售量排名（前10名）:")
    val productSales = dataRDD.map(data => (data.product, data.coffeeSales))
      .reduceByKey(_ + _)
      .sortBy(-_._2)
      .take(10)

    productSales.zipWithIndex.foreach { case ((product, sales), index) =>
      writer.println(f"${index + 1}%2d. $product%-25s: $$${sales}%,.2f")
    }
    writer.println()

    // 按州排名
    writer.println("按州销售量排名:")
    val stateSales = dataRDD.map(data => (data.state, data.coffeeSales))
      .reduceByKey(_ + _)
      .sortBy(-_._2)
      .collect()

    stateSales.zipWithIndex.foreach { case ((state, sales), index) =>
      writer.println(f"${index + 1}%2d. $state%-15s: $$${sales}%,.2f")
    }
    writer.println()

    // 按市场排名
    writer.println("按市场销售量排名:")
    val marketSales = dataRDD.map(data => (data.market, data.coffeeSales))
      .reduceByKey(_ + _)
      .sortBy(-_._2)
      .collect()

    marketSales.zipWithIndex.foreach { case ((market, sales), index) =>
      writer.println(f"${index + 1}%2d. $market%-15s: $$${sales}%,.2f")
    }
    writer.println()
  }

  // 3. 销售量与州的关系
  def salesByState(dataRDD: org.apache.spark.rdd.RDD[CoffeeData], writer: PrintWriter): Unit = {
    writer.println("="*60)
    writer.println("3. 咖啡销售量与州的关系分析")
    writer.println("="*60)

    val stateStats = dataRDD.map(data => (data.state, (data.coffeeSales, data.profit, 1)))
      .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3))
      .map { case (state, (totalSales, totalProfit, count)) =>
        (state, totalSales, totalProfit, count, totalSales / count, totalProfit / count)
      }
      .sortBy(-_._2)
      .collect()

    writer.println(f"${"州名"}%-15s ${"总销售额"}%12s ${"总利润"}%12s ${"记录数"}%8s ${"平均销售额"}%12s ${"平均利润"}%12s")
    writer.println("-" * 80)

    stateStats.foreach { case (state, totalSales, totalProfit, count, avgSales, avgProfit) =>
      writer.println(f"$state%-15s $$${totalSales}%10.2f $$${totalProfit}%10.2f $count%8d $$${avgSales}%10.2f $$${avgProfit}%10.2f")
    }
    writer.println()

    // 计算州之间的销售差异
    val maxSales = stateStats.map(_._2).max
    val minSales = stateStats.map(_._2).min
    val salesRange = maxSales - minSales

    writer.println(f"销售额最高州与最低州差异: $$${salesRange}%,.2f")
    writer.println(f"销售额变异系数: ${(salesRange / stateStats.map(_._2).sum * stateStats.length) * 100}%.2f%%")
    writer.println()
  }

  // 4. 销售量与市场的关系
  def salesByMarket(dataRDD: org.apache.spark.rdd.RDD[CoffeeData], writer: PrintWriter): Unit = {
    writer.println("="*60)
    writer.println("4. 咖啡销售量与市场的关系分析")
    writer.println("="*60)

    val marketStats = dataRDD.map(data => (data.market, (data.coffeeSales, data.profit, 1)))
      .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3))
      .map { case (market, (totalSales, totalProfit, count)) =>
        (market, totalSales, totalProfit, count, totalSales / count, totalProfit / count)
      }
      .sortBy(-_._2)
      .collect()

    writer.println(f"${"市场"}%-15s ${"总销售额"}%12s ${"总利润"}%12s ${"记录数"}%8s ${"平均销售额"}%12s ${"平均利润"}%12s")
    writer.println("-" * 80)

    marketStats.foreach { case (market, totalSales, totalProfit, count, avgSales, avgProfit) =>
      writer.println(f"$market%-15s $$${totalSales}%10.2f $$${totalProfit}%10.2f $count%8d $$${avgSales}%10.2f $$${avgProfit}%10.2f")
    }
    writer.println()

    // 市场规模分析
    writer.println("按市场规模分析:")
    val marketSizeStats = dataRDD.map(data => (data.marketSize, (data.coffeeSales, 1)))
      .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2))
      .map { case (size, (totalSales, count)) => (size, totalSales, count, totalSales / count) }
      .sortBy(-_._2)
      .collect()

    marketSizeStats.foreach { case (size, totalSales, count, avgSales) =>
      writer.println(f"$size%-20s: 总销售额 $$${totalSales}%,.2f, 记录数 $count%,d, 平均销售额 $$${avgSales}%.2f")
    }
    writer.println()
  }

  // 5. 平均利润和售价分析
  def profitAndPriceAnalysis(dataRDD: org.apache.spark.rdd.RDD[CoffeeData], writer: PrintWriter): Unit = {
    writer.println("="*60)
    writer.println("5. 咖啡平均利润和售价分析")
    writer.println("="*60)

    val totalRecords = dataRDD.count()
    val avgProfit = dataRDD.map(_.profit).sum() / totalRecords
    val avgSales = dataRDD.map(_.coffeeSales).sum() / totalRecords
    val avgMargin = dataRDD.map(_.margin).sum() / totalRecords
    val avgCogs = dataRDD.map(_.cogs).sum() / totalRecords

    writer.println(f"整体平均指标:")
    writer.println(f"  平均利润: $$${avgProfit}%.2f")
    writer.println(f"  平均销售额: $$${avgSales}%.2f")
    writer.println(f"  平均利润率: $$${avgMargin}%.2f")
    writer.println(f"  平均成本: $$${avgCogs}%.2f")
    writer.println(f"  利润率: ${(avgProfit / avgSales) * 100}%.2f%%")
    writer.println()

    // 按产品类型分析
    writer.println("按产品类型分析:")
    val productTypeStats = dataRDD.map(data => (data.productType, (data.profit, data.coffeeSales, data.margin, data.cogs, 1)))
      .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3, a._4 + b._4, a._5 + b._5))
      .map { case (productType, (totalProfit, totalSales, totalMargin, totalCogs, count)) =>
        (productType, totalProfit / count, totalSales / count, totalMargin / count, totalCogs / count, count)
      }
      .sortBy(-_._2)
      .collect()

    writer.println(f"${"产品类型"}%-15s ${"平均利润"}%12s ${"平均销售额"}%12s ${"平均利润率"}%12s ${"平均成本"}%12s ${"记录数"}%8s")
    writer.println("-" * 85)

    productTypeStats.foreach { case (productType, avgProfit, avgSales, avgMargin, avgCogs, count) =>
      writer.println(f"$productType%-15s $$${avgProfit}%10.2f $$${avgSales}%10.2f $$${avgMargin}%10.2f $$${avgCogs}%10.2f $count%8d")
    }
    writer.println()
  }

  // 6. 利润、售价和销售量关系
  def profitSalesPriceRelation(dataRDD: org.apache.spark.rdd.RDD[CoffeeData], writer: PrintWriter): Unit = {
    writer.println("="*60)
    writer.println("6. 利润、售价和销售量关系分析")
    writer.println("="*60)

    // 按销售额区间分析利润
    val salesRanges = dataRDD.map { data =>
      val salesRange = data.coffeeSales match {
        case s if s < 100 => "0-100"
        case s if s < 200 => "100-200"
        case s if s < 300 => "200-300"
        case s if s < 400 => "300-400"
        case s if s < 500 => "400-500"
        case _ => "500+"
      }
      (salesRange, (data.profit, data.margin, 1))
    }.reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3))
      .map { case (range, (totalProfit, totalMargin, count)) =>
        (range, totalProfit / count, totalMargin / count, count)
      }
      .sortBy(_._1)
      .collect()

    writer.println("按销售额区间分析:")
    writer.println(f"${"销售额区间"}%-12s ${"平均利润"}%12s ${"平均利润率"}%12s ${"记录数"}%8s")
    writer.println("-" * 50)

    salesRanges.foreach { case (range, avgProfit, avgMargin, count) =>
      writer.println(f"$range%-12s $$${avgProfit}%10.2f $$${avgMargin}%10.2f $count%8d")
    }
    writer.println()

    // 计算相关性（简单线性相关）
    val salesProfitData = dataRDD.map(data => (data.coffeeSales, data.profit)).collect()
    val correlation = calculateCorrelation(salesProfitData)

    writer.println(f"销售额与利润的相关系数: ${correlation}%.4f")
    if (correlation > 0.7) writer.println("强正相关")
    else if (correlation > 0.3) writer.println("中等正相关")
    else if (correlation > -0.3) writer.println("弱相关")
    else if (correlation > -0.7) writer.println("中等负相关")
    else writer.println("强负相关")
    writer.println()
  }

  // 7. 利润、销售量与成本关系
  def profitSalesCostRelation(dataRDD: org.apache.spark.rdd.RDD[CoffeeData], writer: PrintWriter): Unit = {
    writer.println("="*60)
    writer.println("7. 利润、销售量与成本关系分析")
    writer.println("="*60)

    // 按成本区间分析
    val costRanges = dataRDD.map { data =>
      val costRange = data.cogs match {
        case c if c < 50 => "0-50"
        case c if c < 100 => "50-100"
        case c if c < 150 => "100-150"
        case c if c < 200 => "150-200"
        case c if c < 250 => "200-250"
        case _ => "250+"
      }
      (costRange, (data.profit, data.coffeeSales, data.totalExpenses, 1))
    }.reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3, a._4 + b._4))
      .map { case (range, (totalProfit, totalSales, totalExpenses, count)) =>
        (range, totalProfit / count, totalSales / count, totalExpenses / count, count)
      }
      .sortBy(_._1)
      .collect()

    writer.println("按成本区间分析:")
    writer.println(f"${"成本区间"}%-12s ${"平均利润"}%12s ${"平均销售额"}%12s ${"平均总费用"}%12s ${"记录数"}%8s")
    writer.println("-" * 65)

    costRanges.foreach { case (range, avgProfit, avgSales, avgExpenses, count) =>
      writer.println(f"$range%-12s $$${avgProfit}%10.2f $$${avgSales}%10.2f $$${avgExpenses}%10.2f $count%8d")
    }
    writer.println()

    // 成本效率分析
    writer.println("成本效率分析（利润/成本比率）:")
    val costEfficiency = dataRDD.map { data =>
      val efficiency = if (data.cogs > 0) data.profit / data.cogs else 0
      (data.state, efficiency)
    }.groupByKey()
      .map { case (state, efficiencies) =>
        val avgEfficiency = efficiencies.sum / efficiencies.size
        (state, avgEfficiency)
      }
      .sortBy(-_._2)
      .collect()

    costEfficiency.foreach { case (state, efficiency) =>
      writer.println(f"$state%-15s: ${efficiency}%.4f")
    }
    writer.println()
  }

  // 8. 产品属性与各指标关系
  def productAttributeAnalysis(dataRDD: org.apache.spark.rdd.RDD[CoffeeData], writer: PrintWriter): Unit = {
    writer.println("="*60)
    writer.println("8. 产品属性与各指标关系分析")
    writer.println("="*60)

    // 按咖啡类型分析
    writer.println("按咖啡类型分析:")
    val coffeeTypeStats = dataRDD.map(data => (data.coffeeType, (data.coffeeSales, data.profit, data.margin, data.cogs, 1)))
      .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3, a._4 + b._4, a._5 + b._5))
      .map { case (coffeeType, (totalSales, totalProfit, totalMargin, totalCogs, count)) =>
        (coffeeType, totalSales / count, totalProfit / count, totalMargin / count, totalCogs / count, count)
      }
      .sortBy(-_._2)
      .collect()

    writer.println(f"${"咖啡类型"}%-15s ${"平均销售额"}%12s ${"平均利润"}%12s ${"平均利润率"}%12s ${"平均成本"}%12s ${"记录数"}%8s")
    writer.println("-" * 90)

    coffeeTypeStats.foreach { case (coffeeType, avgSales, avgProfit, avgMargin, avgCogs, count) =>
      writer.println(f"$coffeeType%-15s $$${avgSales}%10.2f $$${avgProfit}%10.2f $$${avgMargin}%10.2f $$${avgCogs}%10.2f $count%8d")
    }
    writer.println()

    // 按产品分析（前10名）
    writer.println("按具体产品分析（前10名）:")
    val productStats = dataRDD.map(data => (data.product, (data.coffeeSales, data.profit, 1)))
      .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3))
      .map { case (product, (totalSales, totalProfit, count)) =>
        (product, totalSales, totalProfit, count, totalSales / count, totalProfit / count)
      }
      .sortBy(-_._2)
      .take(10)

    writer.println(f"${"产品名称"}%-25s ${"总销售额"}%12s ${"总利润"}%12s ${"记录数"}%8s ${"平均销售额"}%12s ${"平均利润"}%12s")
    writer.println("-" * 100)

    productStats.foreach { case (product, totalSales, totalProfit, count, avgSales, avgProfit) =>
      writer.println(f"$product%-25s $$${totalSales}%10.2f $$${totalProfit}%10.2f $count%8d $$${avgSales}%10.2f $$${avgProfit}%10.2f")
    }
    writer.println()
  }

  // 9. 市场规模、地域与销售量关系
  def marketSizeRegionAnalysis(dataRDD: org.apache.spark.rdd.RDD[CoffeeData], writer: PrintWriter): Unit = {
    writer.println("="*60)
    writer.println("9. 市场规模、地域与销售量关系分析")
    writer.println("="*60)

    // 按市场规模和地域组合分析
    val regionMarketStats = dataRDD.map(data => ((data.state, data.marketSize), (data.coffeeSales, data.profit, 1)))
      .reduceByKey((a, b) => (a._1 + b._1, a._2 + b._2, a._3 + b._3))
      .map { case ((state, marketSize), (totalSales, totalProfit, count)) =>
        (state, marketSize, totalSales, totalProfit, count, totalSales / count, totalProfit / count)
      }
      .sortBy(-_._3)
      .collect()

    writer.println(f"${"州"}%-15s ${"市场规模"}%-15s ${"总销售额"}%12s ${"总利润"}%12s ${"记录数"}%8s ${"平均销售额"}%12s ${"平均利润"}%12s")
    writer.println("-" * 105)

    regionMarketStats.foreach { case (state, marketSize, totalSales, totalProfit, count, avgSales, avgProfit) =>
      writer.println(f"$state%-15s $marketSize%-15s $$${totalSales}%10.2f $$${totalProfit}%10.2f $count%8d $$${avgSales}%10.2f $$${avgProfit}%10.2f")
    }
    writer.println()

    // 地域销售分布分析
    writer.println("地域销售分布总结:")
    val regionSummary = dataRDD.map(data => (data.state, data.coffeeSales))
      .groupByKey()
      .map { case (state, sales) =>
        val salesList = sales.toList
        val total = salesList.sum
        val avg = total / salesList.length
        val max = salesList.max
        val min = salesList.min
        (state, total, avg, max, min, salesList.length)
      }
      .sortBy(-_._2)
      .collect()

    regionSummary.foreach { case (state, total, avg, max, min, count) =>
      writer.println(f"$state%-15s: 总计 $$${total}%,.2f, 平均 $$${avg}%.2f, 最高 $$${max}%.2f, 最低 $$${min}%.2f, 记录数 $count%,d")
    }
    writer.println()
  }

  // 计算相关系数的辅助函数
  def calculateCorrelation(data: Array[(Double, Double)]): Double = {
    if (data.length < 2) return 0.0

    val n = data.length
    val sumX = data.map(_._1).sum
    val sumY = data.map(_._2).sum
    val sumXY = data.map(p => p._1 * p._2).sum
    val sumX2 = data.map(p => p._1 * p._1).sum
    val sumY2 = data.map(p => p._2 * p._2).sum

    val numerator = n * sumXY - sumX * sumY
    val denominator = math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY))

    if (denominator == 0) 0.0 else numerator / denominator
  }
}
