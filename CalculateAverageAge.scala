import org.apache.spark.SparkContext
import org.apache.spark.SparkConf

/**
 * 使用Spark RDD计算人口平均年龄
 * 功能：读取peopleage.txt文件，计算所有人口的平均年龄
 */
object CalculateAverageAge {
  
  def main(args: Array[String]): Unit = {
    // 设置Spark配置
    val conf = new SparkConf()
      .setAppName("CalculateAverageAge")
      .setMaster("local[*]") // 使用本地模式，使用所有可用核心
    
    // 创建SparkContext
    val sc = new SparkContext(conf)
    
    try {
      // 输入文件路径
      val inputFile = if (args.length > 0) args(0) else "peopleage.txt"
      
      println(s"开始处理文件：$inputFile")
      
      // 读取文件创建RDD
      val textRDD = sc.textFile(inputFile)
      
      println(s"文件总行数：${textRDD.count()}")
      
      // 解析数据，提取年龄
      val ageRDD = textRDD.map { line =>
        val parts = line.split("\t")
        if (parts.length >= 2) {
          try {
            parts(1).toInt // 第二列是年龄
          } catch {
            case _: NumberFormatException =>
              println(s"警告：无法解析年龄数据：$line")
              0 // 如果解析失败，使用0作为默认值
          }
        } else {
          println(s"警告：数据格式不正确：$line")
          0
        }
      }.filter(_ > 0) // 过滤掉无效数据
      
      // 缓存RDD，因为会多次使用
      ageRDD.cache()
      
      // 统计有效数据条数
      val validCount = ageRDD.count()
      println(s"有效数据条数：$validCount")
      
      if (validCount > 0) {
        // 计算总年龄
        val totalAge = ageRDD.reduce(_ + _)
        
        // 计算平均年龄
        val averageAge = totalAge.toDouble / validCount
        
        // 计算其他统计信息
        val maxAge = ageRDD.max()
        val minAge = ageRDD.min()
        
        // 年龄分布统计
        val ageGroups = ageRDD.map { age =>
          age match {
            case a if a < 20 => "18-19岁"
            case a if a < 30 => "20-29岁"
            case a if a < 40 => "30-39岁"
            case a if a < 50 => "40-49岁"
            case a if a < 60 => "50-59岁"
            case a if a < 70 => "60-69岁"
            case _ => "70岁以上"
          }
        }.countByValue()
        
        // 输出结果
        println("\n" + "="*50)
        println("人口年龄统计结果")
        println("="*50)
        println(f"总人数：$validCount%,d 人")
        println(f"平均年龄：$averageAge%.2f 岁")
        println(f"最大年龄：$maxAge 岁")
        println(f"最小年龄：$minAge 岁")
        println(f"年龄总和：$totalAge%,d 岁")
        
        println("\n年龄分布：")
        println("-" * 30)
        ageGroups.toSeq.sortBy(_._1).foreach { case (group, count) =>
          val percentage = (count.toDouble / validCount) * 100
          println(f"$group%-10s: $count%,6d 人 ($percentage%5.1f%%)")
        }
        
        // 显示前20条原始数据作为验证
        println("\n前20条数据验证：")
        println("-" * 30)
        val sampleData = textRDD.take(20)
        sampleData.foreach(println)
        
      } else {
        println("错误：没有找到有效的年龄数据！")
      }
      
    } catch {
      case e: Exception =>
        println(s"处理过程中发生错误：${e.getMessage}")
        e.printStackTrace()
    } finally {
      // 停止SparkContext
      sc.stop()
    }
  }
}
