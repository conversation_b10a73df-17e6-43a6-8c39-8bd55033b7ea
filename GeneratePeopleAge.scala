import scala.util.Random
import java.io.PrintWriter
import java.io.File

/**
 * 生成人口年龄模拟数据
 * 功能：创建包含序号和年龄的数据文件peopleage.txt
 * 数据格式：序号    年龄
 */
object GeneratePeopleAge {
  
  def main(args: Array[String]): Unit = {
    // 设置参数
    val numRecords = if (args.length > 0) args(0).toInt else 1000
    val fileName = if (args.length > 1) args(1) else "peopleage.txt"
    
    println(s"开始生成 $numRecords 条人口年龄数据...")
    
    // 创建随机数生成器
    val random = new Random()
    
    // 创建文件写入器
    val writer = new PrintWriter(new File(fileName))
    
    try {
      // 生成数据并写入文件
      for (i <- 1 to numRecords) {
        // 生成18-80岁之间的随机年龄
        val age = 18 + random.nextInt(63) // 18 + (0-62) = 18-80
        
        // 写入格式：序号    年龄
        writer.println(s"$i\t$age")
        
        // 每1000条记录显示进度
        if (i % 1000 == 0) {
          println(s"已生成 $i 条记录...")
        }
      }
      
      println(s"数据生成完成！文件保存为：$fileName")
      println(s"总共生成 $numRecords 条记录")
      
      // 显示前10条数据作为示例
      println("\n前10条数据示例：")
      println("序号\t年龄")
      println("----\t----")
      
      val source = scala.io.Source.fromFile(fileName)
      val lines = source.getLines().take(10).toList
      source.close()
      
      lines.foreach(println)
      
    } catch {
      case e: Exception =>
        println(s"生成数据时发生错误：${e.getMessage}")
        e.printStackTrace()
    } finally {
      writer.close()
    }
  }
}
