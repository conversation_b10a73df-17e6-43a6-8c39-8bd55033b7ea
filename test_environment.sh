#!/bin/bash

# 环境测试脚本
# 用于验证Spark和Java环境是否正确配置

echo "=========================================="
echo "大数据实时处理技术 - 环境测试脚本"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 测试函数
test_command() {
    local cmd="$1"
    local name="$2"
    
    echo -n "测试 $name... "
    if command -v $cmd >/dev/null 2>&1; then
        echo -e "${GREEN}✓ 通过${NC}"
        return 0
    else
        echo -e "${RED}✗ 失败${NC}"
        return 1
    fi
}

test_path() {
    local path="$1"
    local name="$2"
    
    echo -n "检查 $name... "
    if [ -d "$path" ]; then
        echo -e "${GREEN}✓ 存在${NC} ($path)"
        return 0
    else
        echo -e "${RED}✗ 不存在${NC} ($path)"
        return 1
    fi
}

# 环境变量设置
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.412.b08-1.el7_9.x86_64
export SPARK_HOME=/opt/spark
export PATH=$SPARK_HOME/bin:$PATH

echo "环境变量设置："
echo "JAVA_HOME: $JAVA_HOME"
echo "SPARK_HOME: $SPARK_HOME"
echo ""

# 基础命令测试
echo "1. 基础命令测试"
echo "----------------------------------------"
test_command "java" "Java"
test_command "scala" "Scala"
test_command "scalac" "Scala编译器"
test_command "spark-shell" "Spark Shell"
test_command "spark-submit" "Spark Submit"
echo ""

# 路径测试
echo "2. 路径检查"
echo "----------------------------------------"
test_path "$JAVA_HOME" "Java安装路径"
test_path "$SPARK_HOME" "Spark安装路径"
test_path "$SPARK_HOME/jars" "Spark JAR包路径"
echo ""

# 版本信息
echo "3. 版本信息"
echo "----------------------------------------"
echo -n "Java版本: "
java -version 2>&1 | head -n 1 | cut -d'"' -f2

echo -n "Scala版本: "
if command -v scala >/dev/null 2>&1; then
    scala -version 2>&1 | head -n 1
else
    echo -e "${RED}未安装${NC}"
fi

echo -n "Spark版本: "
if command -v spark-shell >/dev/null 2>&1; then
    spark-shell --version 2>&1 | grep "version" | head -n 1
else
    echo -e "${RED}未安装${NC}"
fi
echo ""

# 文件检查
echo "4. 项目文件检查"
echo "----------------------------------------"
files=("GeneratePeopleAge.scala" "CalculateAverageAge.scala" "CoffeeChainAnalysis.scala" "CoffeeChain.csv" "run_analysis.sh")

for file in "${files[@]}"; do
    echo -n "检查 $file... "
    if [ -f "$file" ]; then
        echo -e "${GREEN}✓ 存在${NC}"
    else
        echo -e "${RED}✗ 缺失${NC}"
    fi
done
echo ""

# 内存检查
echo "5. 系统资源检查"
echo "----------------------------------------"
echo -n "可用内存: "
free -h | grep "Mem:" | awk '{print $7}'

echo -n "CPU核心数: "
nproc

echo -n "磁盘空间: "
df -h . | tail -1 | awk '{print $4}'
echo ""

# 简单编译测试
echo "6. 编译测试"
echo "----------------------------------------"
if [ -f "GeneratePeopleAge.scala" ]; then
    echo -n "测试Scala编译... "
    if scalac GeneratePeopleAge.scala 2>/dev/null; then
        echo -e "${GREEN}✓ 成功${NC}"
        # 清理编译文件
        rm -f GeneratePeopleAge*.class
    else
        echo -e "${RED}✗ 失败${NC}"
    fi
else
    echo -e "${YELLOW}跳过编译测试（源文件不存在）${NC}"
fi
echo ""

# Spark连接测试
echo "7. Spark连接测试"
echo "----------------------------------------"
echo -n "测试Spark本地模式... "
if spark-shell --master local[1] --conf spark.ui.enabled=false --conf spark.sql.warehouse.dir=/tmp/spark-warehouse <<< "sc.parallelize(1 to 10).count(); System.exit(0)" 2>/dev/null | grep -q "res"; then
    echo -e "${GREEN}✓ 成功${NC}"
else
    echo -e "${RED}✗ 失败${NC}"
fi
echo ""

echo "=========================================="
echo "环境测试完成！"
echo "=========================================="

# 建议
echo "建议："
echo "1. 如果有测试失败，请检查对应的安装和配置"
echo "2. 确保所有必需的文件都存在于当前目录"
echo "3. 如果内存不足，考虑调整Spark配置参数"
echo "4. 运行 ./run_analysis.sh 开始实训任务"
echo ""
