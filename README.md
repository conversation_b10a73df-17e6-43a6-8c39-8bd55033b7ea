# 大数据实时处理技术实训项目

## 项目概述

本项目是山东协和学院大数据技术专业《大数据实时处理技术》课程的期末实训项目，包含两个主要任务：

1. **RDD编程统计人口平均年龄**：使用Spark RDD实现人口数据的生成和统计分析
2. **咖啡连锁店数据分析**：基于真实业务数据进行多维度的大数据分析

## 环境要求

- **操作系统**：CentOS 7+ (推荐在hadoop03节点运行)
- **Java版本**：Java 1.8
- **Spark版本**：Spark 3.x
- **Scala版本**：Scala 2.12+
- **内存要求**：至少2GB可用内存

## 环境配置

### 1. 设置环境变量

```bash
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.412.b08-1.el7_9.x86_64
export SPARK_HOME=/opt/spark
export PATH=$SPARK_HOME/bin:$PATH
```

### 2. 验证环境

```bash
java -version
spark-shell --version
scala -version
```

## 项目文件结构

```
lbxx2/
├── GeneratePeopleAge.scala      # 人口年龄数据生成程序
├── CalculateAverageAge.scala    # 平均年龄计算程序
├── CoffeeChainAnalysis.scala    # 咖啡连锁店数据分析程序
├── CoffeeChain.csv             # 咖啡连锁店数据文件
├── run_analysis.sh             # 自动化运行脚本
├── 任务要求.md                  # 实训任务要求
├── 实训报告模版.md              # 完整的实训报告
└── README.md                   # 项目说明文档
```

## 快速开始

### 方法一：使用自动化脚本（推荐）

```bash
# 给脚本添加执行权限
chmod +x run_analysis.sh

# 运行完整的分析流程
./run_analysis.sh
```

### 方法二：手动执行

#### 第一部分：人口年龄统计

```bash
# 1. 生成人口年龄数据
scalac GeneratePeopleAge.scala
scala GeneratePeopleAge 1000 peopleage.txt

# 2. 计算平均年龄
scalac -cp "$SPARK_HOME/jars/*" CalculateAverageAge.scala
jar cf CalculateAverageAge.jar CalculateAverageAge*.class
spark-submit --class CalculateAverageAge --master local[*] CalculateAverageAge.jar peopleage.txt
```

#### 第二部分：咖啡连锁店数据分析

```bash
# 编译和运行咖啡数据分析
scalac -cp "$SPARK_HOME/jars/*" CoffeeChainAnalysis.scala
jar cf CoffeeChainAnalysis.jar CoffeeChainAnalysis*.class
spark-submit --class CoffeeChainAnalysis --master local[*] CoffeeChainAnalysis.jar CoffeeChain.csv
```

## 程序功能说明

### 1. GeneratePeopleAge.scala

**功能**：生成模拟的人口年龄数据
- 生成指定数量的人口记录
- 年龄范围：18-80岁
- 输出格式：序号\t年龄

**使用方法**：
```bash
scala GeneratePeopleAge [记录数] [输出文件名]
# 示例：scala GeneratePeopleAge 1000 peopleage.txt
```

### 2. CalculateAverageAge.scala

**功能**：使用Spark RDD计算人口平均年龄
- 读取peopleage.txt文件
- 计算平均年龄、最大年龄、最小年龄
- 统计年龄分布情况
- 显示详细的统计结果

**核心RDD操作**：
- `textFile()`: 读取文件创建RDD
- `map()`: 数据转换和解析
- `filter()`: 过滤无效数据
- `reduce()`: 聚合计算
- `cache()`: 缓存优化

### 3. CoffeeChainAnalysis.scala

**功能**：咖啡连锁店多维度数据分析
- 数据预处理和基本统计
- 销售量排名分析（按产品、州、市场）
- 销售量与地域关系分析
- 销售量与市场关系分析
- 利润和售价关系分析
- 成本效率分析
- 产品属性分析
- 市场规模和地域综合分析

**分析维度**：
1. 咖啡销售量和State的关系
2. 咖啡销售量和Market的关系
3. 咖啡的平均利润和售价
4. 利润、售价和销售量的关系
5. 利润、销售量与成本的关系
6. 产品属性与各指标的关系
7. 市场规模、地域与销售量的关系

## 输出文件说明

### 1. peopleage.txt
人口年龄模拟数据文件，格式：
```
1	45
2	32
3	67
...
```

### 2. coffee_analysis_results.txt
咖啡连锁店分析结果文件，包含：
- 数据预处理统计信息
- 各维度分析结果
- 排名和分布统计
- 相关性分析结果

## 性能优化建议

### 1. 内存配置
```bash
spark-submit --driver-memory 2g --executor-memory 2g --class YourClass YourJar.jar
```

### 2. 分区优化
```scala
dataRDD.repartition(4) // 根据CPU核心数调整
```

### 3. 缓存策略
```scala
dataRDD.cache() // 缓存频繁使用的RDD
```

## 常见问题解决

### 1. 内存不足错误
- 增加driver和executor内存
- 使用RDD缓存
- 减少数据分区数量

### 2. 编译错误
- 检查Scala版本兼容性
- 确保Spark JAR包路径正确
- 验证Java版本

### 3. 数据解析错误
- 检查CSV文件格式
- 处理特殊字符和空值
- 使用异常处理机制

## 学习要点

### RDD编程核心概念
1. **转换操作**：map、filter、flatMap、groupByKey、reduceByKey
2. **行动操作**：collect、count、reduce、take、saveAsTextFile
3. **缓存机制**：cache、persist
4. **分区策略**：repartition、coalesce

### 数据分析方法
1. **描述性统计**：均值、中位数、方差、分布
2. **分组聚合**：按维度分组统计
3. **排序排名**：多字段排序
4. **相关性分析**：变量间关系分析

## 扩展学习

1. **Spark SQL**：使用DataFrame API提高性能
2. **Spark Streaming**：实时数据处理
3. **Spark MLlib**：机器学习算法
4. **数据可视化**：结合图表库展示结果

## 作者信息

- **姓名**：lbxx
- **专业**：大数据技术
- **班级**：2022级（本）
- **课程**：大数据实时处理技术
- **学校**：山东协和学院

## 版权声明

本项目仅用于教学和学习目的，请勿用于商业用途。
