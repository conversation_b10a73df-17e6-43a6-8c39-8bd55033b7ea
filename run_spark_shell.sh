#!/bin/bash

# 使用spark-shell运行大数据分析的脚本
# 适用于CentOS环境

echo "=========================================="
echo "大数据实时处理技术实训 - Spark Shell运行"
echo "=========================================="

# 设置环境变量
export JAVA_HOME=/usr/lib/jvm/java-1.8.0-openjdk-1.8.0.412.b08-1.el7_9.x86_64
export SPARK_HOME=/opt/spark
export PATH=$SPARK_HOME/bin:$PATH

# 检查环境
echo "检查环境配置..."
if [ ! -d "$JAVA_HOME" ]; then
    echo "错误：Java路径不存在！"
    exit 1
fi

if [ ! -d "$SPARK_HOME" ]; then
    echo "错误：Spark路径不存在！"
    exit 1
fi

echo "环境检查通过！"
echo ""

# 第一步：生成人口年龄数据
echo "第一步：生成人口年龄数据..."
echo "----------------------------------------"

if [ -f "GeneratePeopleAge.scala" ]; then
    echo "编译并运行数据生成程序..."
    scalac GeneratePeopleAge.scala
    if [ $? -eq 0 ]; then
        scala GeneratePeopleAge 1000 peopleage.txt
        echo "人口年龄数据生成完成！"
    else
        echo "编译失败！"
        exit 1
    fi
else
    echo "错误：找不到 GeneratePeopleAge.scala 文件！"
    exit 1
fi

echo ""

# 第二步：使用spark-shell运行人口年龄分析
echo "第二步：使用spark-shell运行人口年龄分析..."
echo "----------------------------------------"

if [ -f "CalculateAverageAge.scala" ] && [ -f "peopleage.txt" ]; then
    echo "启动spark-shell进行人口年龄分析..."
    echo "注意：这将启动spark-shell并自动执行分析..."
    
    # 使用spark-shell执行人口年龄分析
    spark-shell --master local[*] \
                --conf spark.ui.enabled=false \
                --conf spark.sql.warehouse.dir=/tmp/spark-warehouse \
                -i CalculateAverageAge.scala \
                --conf spark.driver.memory=1g \
                --conf spark.executor.memory=1g
    
    echo "人口年龄分析完成！"
else
    echo "错误：缺少必要文件（CalculateAverageAge.scala 或 peopleage.txt）！"
    exit 1
fi

echo ""

# 第三步：使用spark-shell运行咖啡数据分析
echo "第三步：使用spark-shell运行咖啡数据分析..."
echo "----------------------------------------"

if [ -f "CoffeeChainAnalysis.scala" ] && [ -f "CoffeeChain.csv" ]; then
    echo "启动spark-shell进行咖啡数据分析..."
    echo "注意：这将启动spark-shell并自动执行分析..."
    
    # 使用spark-shell执行咖啡数据分析
    spark-shell --master local[*] \
                --conf spark.ui.enabled=false \
                --conf spark.sql.warehouse.dir=/tmp/spark-warehouse \
                -i CoffeeChainAnalysis.scala \
                --conf spark.driver.memory=2g \
                --conf spark.executor.memory=2g
    
    echo "咖啡数据分析完成！"
else
    echo "错误：缺少必要文件（CoffeeChainAnalysis.scala 或 CoffeeChain.csv）！"
    exit 1
fi

echo ""
echo "=========================================="
echo "所有分析任务完成！"
echo "=========================================="

echo "生成的结果文件："
echo "- peopleage.txt: 人口年龄模拟数据"
echo "- age_analysis_results.txt: 人口年龄分析结果"
echo "- coffee_analysis_results.txt: 咖啡连锁店分析结果"

echo ""
echo "查看结果文件："

if [ -f "age_analysis_results.txt" ]; then
    echo ""
    echo "=== 人口年龄分析结果预览 ==="
    head -20 age_analysis_results.txt
fi

if [ -f "coffee_analysis_results.txt" ]; then
    echo ""
    echo "=== 咖啡分析结果预览 ==="
    head -30 coffee_analysis_results.txt
fi

echo ""
echo "完整结果请查看对应的结果文件。"
echo "实训任务执行完成！"
